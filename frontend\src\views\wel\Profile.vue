<template>
  <div class="page-container">
    <main class="page-content">
      <div class="list-container">
        <!-- 用户信息卡片 -->
        <div class="profile-card list-item">
          <div class="avatar-section">
            <div class="avatar">
              <img v-if="userInfo?.avatar" :src="userInfo.avatar" :alt="displayUserInfo.name" />
              <i v-else class="fas fa-user"></i>
            </div>
            <div class="user-info">
              <h2>{{ displayUserInfo.name }}</h2>
              <p>{{ displayUserInfo.position }}</p>
              <p>{{ displayUserInfo.company }}</p>
            </div>
          </div>
          <div class="status-badge">
            <i class="fas fa-check-circle"></i>
            已报名
          </div>
        </div>

        <!-- 功能菜单 -->
        <div class="menu-container">
          <div class="menu-item list-item" @click="handleMenuClick('mySchedule')">
            <div class="menu-icon">
              <i class="fas fa-calendar-check"></i>
            </div>
            <div class="menu-text">
              <span>我的日程</span>
              <small>个人参会日程安排</small>
            </div>
            <i class="fas fa-chevron-right"></i>
          </div>
          <div class="menu-item list-item" @click="handleMenuClick('myDining')">
            <div class="menu-icon">
              <i class="fas fa-utensils"></i>
            </div>
            <div class="menu-text">
              <span>我的用餐</span>
              <small>查看用餐安排和餐券信息</small>
            </div>
            <i class="fas fa-chevron-right"></i>
          </div>
          <div class="menu-item list-item" @click="handleMenuClick('myHotel')">
            <div class="menu-icon">
              <i class="fas fa-bed"></i>
            </div>
            <div class="menu-text">
              <span>我的住宿</span>
              <small>查看住宿详情和房间信息</small>
            </div>
            <i class="fas fa-chevron-right"></i>
          </div>
        </div>

        <!-- 设置和退出 -->
        <div class="settings-container">
            <!-- <div class="menu-item list-item" @click="handleMenuClick('settings')">
            <div class="menu-icon">
              <i class="fas fa-cog"></i>
            </div>
            <div class="menu-text">
              <span>设置</span>
              <small>应用设置和偏好</small>
            </div>
            <i class="fas fa-chevron-right"></i>
          </div> -->
          <div class="menu-item list-item logout-item" @click="handleLogout">
            <div class="menu-icon">
              <i class="fas fa-sign-out-alt"></i>
            </div>
            <div class="menu-text">
              <span>退出登录</span>
              <small>安全退出当前账户</small>
            </div>
            <i class="fas fa-chevron-right"></i>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>
<script>
import { useUserStore } from '@/store/mobile/user'

export default {
  name: 'Profile',
  setup() {
    // 使用钉钉登录的 Pinia store
    const dingUserStore = useUserStore()
    return {
      dingUserStore
    }
  },
  computed: {
    // 使用钉钉登录的用户信息
    userInfo() {
      return this.dingUserStore.userInfo || {};
    },
    displayUserInfo() {
      const userInfo = this.userInfo;
      return {
        name: userInfo?.user_name || userInfo?.name || userInfo?.account || '钉钉用户',
        position: userInfo?.role_name || userInfo?.position || userInfo?.title || '参会人员',
        company: userInfo?.dept_name || userInfo?.company || userInfo?.organization || ''
      }
    }
  },
  mounted() {
    this.loadUserInfo();
  },
  methods: {
    loadUserInfo() {
      // 如果钉钉store中没有用户信息，尝试从localStorage恢复
      if (!this.userInfo || Object.keys(this.userInfo).length === 0) {
        this.dingUserStore.initUserInfo();
      }
    },
    handleMenuClick(menuType) {
      switch (menuType) {
        case 'myInfo':
          this.showMyInfo();
          break;
        case 'myTickets':
          this.showMyTickets();
          break;
        case 'mySchedule':
          this.showMySchedule();
          break;
        case 'myPhotos':
          this.showMyPhotos();
          break;
        case 'myDining':
          this.showMyDining();
          break;
        case 'myHotel':
          this.showMyHotel();
          break;
        case 'settings':
          this.showSettings();
          break;
        default:
          console.log('未知菜单类型:', menuType);
      }
    },
    showMyInfo() {
      if (this.$message) {
        this.$message.info('我的信息功能');
      } else {
        alert('我的信息功能\n（演示功能）');
      }
    },
    showMyTickets() {
      if (this.$message) {
        this.$message.info('我的门票功能');
      } else {
        alert('我的门票功能\n（演示功能）');
      }
    },
    showMySchedule() {
      this.$emit('navigate', 'my-schedule');
    },
    showMyPhotos() {
      if (this.$message) {
        this.$message.info('我的相册功能');
      } else {
        alert('我的相册功能\n（演示功能）');
      }
    },
    showMyDining() {
      this.$emit('navigate', 'my-dining');
    },
    showMyHotel() {
      this.$emit('navigate', 'my-accommodation');
    },
    showSettings() {
      if (this.$message) {
        this.$message.info('设置功能');
      } else {
        alert('设置功能\n（演示功能）');
      }
    },
    handleLogout() {
      const confirmLogout = () => {
        // 清除钉钉登录数据
        this.dingUserStore.clearUserInfo();

        // 显示退出成功消息
        if (this.$message) {
          this.$message.success('已退出登录');
        } else {
          alert('已退出登录');
        }

        // 跳转到钉钉登录页面
        setTimeout(() => {
          this.$router.push('/dinglogin');
        }, 1000);
      };

      if (this.$confirm) {
        this.$confirm('确定要退出登录吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          confirmLogout();
        }).catch(() => {
          // 用户取消
        });
      } else {
        if (confirm('确定要退出登录吗？')) {
          confirmLogout();
        }
      }
    }
  }
}
</script>
<style scoped>
/* 页面通用样式 */
.page-container {
  width: 100%;
  min-height: 85vh;
  background-size: cover;
  position: relative;
  z-index: 1;
  margin: 0 auto;
  box-sizing: border-box;
}

.page-content {
  margin-top: 20px;
  min-height: calc(100vh - 80px);
}

/* 容器样式 - 采用与直播页面一致的磨砂玻璃效果 */
.list-container {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(12px);
  border-radius: 15px;
  padding: 10px;
  border: 1px solid rgba(255, 255, 255, 0.15);
  box-shadow: inset 0 0 10px rgba(255, 255, 255, 0.05),
  0 10px 25px rgba(0, 0, 0, 0.3);
  animation: slideInUp 0.6s ease forwards;
  max-width: 1400px;
  margin: 0 auto;
}

/* 列表项样式 - 统一卡片风格 */
.list-item {
  background: rgba(255, 255, 255, 0.06);
  backdrop-filter: blur(10px);
  border-radius: 10px;
  padding: 20px;
  margin-bottom: 15px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-left: 4px solid rgba(7, 211, 240, 0.7);
  transition: all 0.3s ease;
  animation: slideInUp 0.6s ease forwards;
  position: relative;
  overflow: hidden;
}

/* 卡片悬停效果 - 与直播页面统一 */
.list-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: -150%;
  width: 300%;
  height: 100%;
  background: linear-gradient(
      120deg,
      rgba(255, 255, 255, 0) 0%,
      rgba(255, 255, 255, 0.2) 50%,
      rgba(255, 255, 255, 0) 100%
  );
  transform: skewX(-20deg);
  z-index: 1;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.list-item:hover::before {
  opacity: 1;
  animation: shimmer 1.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.list-item:hover {
  transform: translateX(3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  background: rgba(255, 255, 255, 0.09);
}

/* 用户信息卡片样式 */
.profile-card {
  display: flex;
  align-items: center;
}

.avatar-section {
  display: flex;
  align-items: center;
  gap: 15px;
}

.avatar {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, rgba(7, 211, 240, 0.7), rgba(7, 211, 240, 0.4));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 15px rgba(7, 211, 240, 0.3);
}

.avatar i {
  font-size: 28px;
  color: white;
}

.avatar img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}

.user-info h2 {
  color: #ffffff;
  font-size: 20px;
  margin-bottom: 5px;
  font-weight: 600;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.user-info p {
  color: rgba(255, 255, 255, 0.8);
  font-size: 13px;
  margin: 2px 0;
  line-height: 1.4;
}

.status-badge {
  margin-left: auto;
  background: linear-gradient(135deg, rgba(7, 211, 240, 0.7), rgba(7, 211, 240, 0.4));
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 5px;
  box-shadow: 0 2px 8px rgba(7, 211, 240, 0.3);
}

.status-badge i {
  font-size: 12px;
}

/* 功能菜单样式 */
.menu-container {
  margin-bottom: 25px;
}

.menu-item {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.menu-icon {
  width: 45px;
  height: 45px;
  background: rgba(7, 211, 240, 0.1);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
}

.menu-icon i {
  font-size: 20px;
  color: #07D3F0;
  text-shadow: 0 0 10px rgba(7, 211, 240, 0.5);
}

.menu-text {
  flex: 1;
}

.menu-text span {
  color: #ffffff;
  font-size: 16px;
  font-weight: 500;
  display: block;
  margin-bottom: 3px;
}

.menu-text small {
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
  line-height: 1.3;
}

.menu-item > i {
  color: rgba(255, 255, 255, 0.5);
  font-size: 14px;
  margin-left: 10px;
}

/* 设置容器样式 */
.settings-container {
  border-top: 1px solid rgba(7, 211, 240, 0.1);
  padding-top: 20px;
}

.logout-item {
  border-left: 4px solid rgba(255, 99, 71, 0.7);
}

.logout-item .menu-icon {
  background: rgba(255, 99, 71, 0.1);
}

.logout-item .menu-icon i {
  color: #ff6347;
}

/* 动画效果 - 与直播页面统一 */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
  }
  100% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
  }
}

/* 为菜单项添加交错动画 */
.menu-item:nth-child(1) { animation-delay: 0.1s; }
.menu-item:nth-child(2) { animation-delay: 0.2s; }
.menu-item:nth-child(3) { animation-delay: 0.3s; }
.menu-item:nth-child(4) { animation-delay: 0.4s; }

/* 响应式设计 */
@media (max-width: 480px) {
  .list-container {
    padding: 15px;
  }

  .profile-card {
    padding: 15px;
  }

  .avatar {
    width: 50px;
    height: 50px;
  }

  .avatar i {
    font-size: 24px;
  }

  .user-info h2 {
    font-size: 18px;
  }

  .menu-item {
    padding: 12px;
  }

  .menu-icon {
    width: 40px;
    height: 40px;
  }

  .menu-icon i {
    font-size: 18px;
  }

  .menu-text span {
    font-size: 14px;
  }

  .menu-text small {
    font-size: 11px;
  }
}
</style>
