-- 2025-07-29 用户日程信息表
-- 用于存储用户的日程、用餐、住宿信息，给个人中心使用

DROP TABLE IF EXISTS "hy_user_schedule";
CREATE TABLE hy_user_schedule (
    id BIGINT PRIMARY KEY,                    -- 主键，自增
    user_id BIGINT NOT NULL,                  -- 用户ID，关联blade_user表
    schedule_content TEXT,                    -- 日程信息（富文本）
    dining_info TEXT,                        -- 用餐信息（JSON格式）
    accommodation_info TEXT,                 -- 住宿信息（JSON格式）
    create_user BIGINT,                       -- 创建人
    create_dept BIGINT,                       -- 创建部门
    create_time TIMESTAMP(6),                 -- 创建时间
    update_user BIGINT,                       -- 更新人
    update_time TIMESTAMP(6),                 -- 更新时间
    status INT DEFAULT 1,                     -- 状态
    is_deleted INT DEFAULT 0                  -- 是否删除
);

-- 添加表注释
COMMENT ON TABLE hy_user_schedule IS '用户日程信息表';
COMMENT ON COLUMN hy_user_schedule.id IS '主键，自增';
COMMENT ON COLUMN hy_user_schedule.user_id IS '用户ID，关联blade_user表';
COMMENT ON COLUMN hy_user_schedule.schedule_content IS '日程信息（富文本）';
COMMENT ON COLUMN hy_user_schedule.dining_info IS '用餐信息（JSON格式）';
COMMENT ON COLUMN hy_user_schedule.accommodation_info IS '住宿信息（JSON格式）';
COMMENT ON COLUMN hy_user_schedule.create_user IS '创建人';
COMMENT ON COLUMN hy_user_schedule.create_dept IS '创建部门';
COMMENT ON COLUMN hy_user_schedule.create_time IS '创建时间';
COMMENT ON COLUMN hy_user_schedule.update_user IS '更新人';
COMMENT ON COLUMN hy_user_schedule.update_time IS '更新时间';
COMMENT ON COLUMN hy_user_schedule.status IS '状态';
COMMENT ON COLUMN hy_user_schedule.is_deleted IS '是否删除';

---插入示例数据
INSERT INTO "public"."hy_user_schedule" ("id", "user_id", "schedule_content", "dining_info", "accommodation_info", "create_user", "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted") VALUES (1951198110297788416, 1123598821738675201, '我的会议日程<p><strong>第一天（2025-07-29）</strong></p><ul><li>09:00-10:30 开幕式及主旨演讲</li><li>10:45-12:00 技术分享会</li><li>14:00-15:30 圆桌讨论</li><li>16:00-17:00 闭幕式</li></ul><p><strong>第二天（2025-07-30）</strong></p><ul><li>09:00-10:30 分会场讨论</li><li>10:45-12:00 成果展示</li></ul>', '{
  "currentDate": "2025年9月15日",
  "activeMenu": "lunch",
  "mealStatus": [
    {
      "type": "breakfast",
      "name": "早餐",
      "icon": "fas fa-coffee",
      "status": "completed",
      "statusIcon": "fas fa-check",
      "statusText": "已用餐"
    },
    {
      "type": "lunch",
      "name": "午餐",
      "icon": "fas fa-utensils",
      "status": "pending",
      "statusIcon": "fas fa-clock",
      "statusText": "待用餐"
    },
    {
      "type": "dinner",
      "name": "晚餐",
      "icon": "fas fa-moon",
      "status": "pending",
      "statusIcon": "fas fa-clock",
      "statusText": "待用餐"
    }
  ],
  "tickets": [
    {
      "id": 1,
      "type": "早餐券",
      "icon": "fas fa-coffee",
      "status": "used",
      "statusText": "已使用",
      "time": "07:30 - 08:30",
      "location": "大厦1楼餐厅",
      "usedTime": "08:15"
    },
    {
      "id": 2,
      "type": "午餐券",
      "icon": "fas fa-utensils",
      "status": "available",
      "statusText": "可使用",
      "time": "12:00 - 13:30",
      "location": "大厦1楼餐厅",
      "menu": "商务套餐A"
    },
    {
      "id": 3,
      "type": "晚餐券",
      "icon": "fas fa-moon",
      "status": "available",
      "statusText": "可使用",
      "time": "18:00 - 19:30",
      "location": "大厦2楼宴会厅",
      "menu": "欢迎晚宴"
    }
  ],
  "menus": [
    { "type": "lunch", "name": "午餐" },
    { "type": "dinner", "name": "晚餐" }
  ],
  "menuData": {
    "lunch": {
      "title": "商务套餐A",
      "dishes": [
        { "name": "白切鸡", "description": "精选土鸡，口感鲜嫩" },
        { "name": "清蒸鲈鱼", "description": "新鲜鲈鱼，营养丰富" },
        { "name": "时令蔬菜", "description": "当季新鲜蔬菜" },
        { "name": "白米饭", "description": "优质大米" },
        { "name": "例汤", "description": "营养汤品" }
      ]
    },
    "dinner": {
      "title": "欢迎晚宴",
      "dishes": [
        { "name": "红烧狮子头", "description": "传统名菜，肉质鲜美" },
        { "name": "蒜蓉扇贝", "description": "新鲜扇贝，蒜香浓郁" },
        { "name": "宫保鸡丁", "description": "经典川菜，香辣可口" },
        { "name": "麻婆豆腐", "description": "嫩滑豆腐，麻辣鲜香" },
        { "name": "水果拼盘", "description": "时令水果" }
      ]
    }
  }
}', '{
  "hotelData": {
    "name": "广州大酒店",
    "rating": 5,
    "details": [
      {
        "icon": "fas fa-map-marker-alt",
        "title": "酒店地址",
        "content": "广州市天河区珠江新城花城大道"
      },
      {
        "icon": "fas fa-walking",
        "title": "距离会场",
        "content": "步行3分钟到达会议中心"
      },
      {
        "icon": "fas fa-phone",
        "title": "联系电话",
        "content": "020-87654321"
      }
    ]
  },
  "roomData": {
    "type": "商务标准间",
    "number": "1208",
    "features": [
      { "icon": "fas fa-wifi", "name": "免费WiFi" },
      { "icon": "fas fa-tv", "name": "液晶电视" },
      { "icon": "fas fa-snowflake", "name": "空调" },
      { "icon": "fas fa-bath", "name": "独立卫浴" }
    ]
  },
  "checkinData": [
    {
      "label": "入住时间：",
      "value": "2025年9月14日 14:00"
    },
    {
      "label": "退房时间：",
      "value": "2025年9月17日 12:00"
    },
    {
      "label": "住宿天数：",
      "value": "3晚"
    }
  ],
  "hotelServices": [
    { "icon": "fas fa-concierge-bell", "name": "24小时前台" },
    { "icon": "fas fa-utensils", "name": "餐厅服务" },
    { "icon": "fas fa-dumbbell", "name": "健身中心" },
    { "icon": "fas fa-swimming-pool", "name": "游泳池" },
    { "icon": "fas fa-car", "name": "停车场" },
    { "icon": "fas fa-luggage-cart", "name": "行李服务" }
  ],
  "transportData": [
    {
      "icon": "fas fa-plane",
      "title": "从机场",
      "description": "白云机场 → 地铁3号线 → 珠江新城站",
      "time": "约45分钟"
    },
    {
      "icon": "fas fa-train",
      "title": "从火车站",
      "description": "广州南站 → 地铁2号线转3号线 → 珠江新城站",
      "time": "约30分钟"
    },
    {
      "icon": "fas fa-subway",
      "title": "地铁",
      "description": "3号线/5号线珠江新城站A出口",
      "time": "步行3分钟"
    }
  ],
  "contactData": [
    {
      "icon": "fas fa-phone",
      "type": "酒店前台",
      "value": "020-87654321"
    },
    {
      "icon": "fas fa-headset",
      "type": "客服热线",
      "value": "************"
    },
    {
      "icon": "fas fa-exclamation-triangle",
      "type": "紧急联系",
      "value": "138-0000-0000"
    }
  ],
  "tips": [
    {
      "icon": "fas fa-clock",
      "title": "入住时间",
      "content": "标准入住时间为14:00，如需提前入住请联系前台"
    },
    {
      "icon": "fas fa-id-card",
      "title": "证件要求",
      "content": "入住时请携带有效身份证件办理入住手续"
    },
    {
      "icon": "fas fa-wifi",
      "title": "网络服务",
      "content": "酒店提供免费WiFi，密码请咨询前台"
    },
    {
      "icon": "fas fa-smoking-ban",
      "title": "禁烟提醒",
      "content": "酒店客房内禁止吸烟，设有专门的吸烟区域"
    }
  ]
}', 1123598821738675201, 1123598813738675201, '2025-07-29 07:12:59', 1123598821738675201, '2025-08-01 16:56:14.507', 1, 0);



-- 创建索引
CREATE INDEX idx_hy_user_schedule_user_id ON hy_user_schedule(user_id);
CREATE INDEX idx_hy_user_schedule_status ON hy_user_schedule(status);
CREATE INDEX idx_hy_user_schedule_is_deleted ON hy_user_schedule(is_deleted);

-- 2025-07-30 为hy_sub_venue表添加视频URL和PDF URL字段
ALTER TABLE hy_sub_venue
    ADD COLUMN video_url VARCHAR(500),
    ADD COLUMN pdf_url VARCHAR(500);

COMMENT ON COLUMN hy_sub_venue.video_url IS '视频URL';
COMMENT ON COLUMN hy_sub_venue.pdf_url IS 'PDF文档URL';

-- 2025-08-01 为blade_user表添加工号、房号、会议座位号字段
ALTER TABLE blade_user
    ADD COLUMN employee_number VARCHAR(50),
    ADD COLUMN room_number VARCHAR(50),
    ADD COLUMN meeting_seat_number VARCHAR(50);

COMMENT ON COLUMN blade_user.employee_number IS '工号';
COMMENT ON COLUMN blade_user.room_number IS '房号';
COMMENT ON COLUMN blade_user.meeting_seat_number IS '会议座位号';

-- 2025-08-01 添加钉钉用户表
DROP TABLE IF EXISTS "public"."ms_dingapp_user";
CREATE TABLE "public"."ms_dingapp_user" (
                                            "id" int8 NOT NULL,
                                            "user_id" int8,
                                            "union_id" varchar(255) COLLATE "pg_catalog"."default",
                                            "create_user" int8,
                                            "create_dept" int8,
                                            "create_time" timestamp(6),
                                            "update_user" int8,
                                            "update_time" timestamp(6),
                                            "status" int4,
                                            "is_deleted" int4,
                                            "tenant_id" varchar(12) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."ms_dingapp_user"."user_id" IS '用户ID';
COMMENT ON COLUMN "public"."ms_dingapp_user"."union_id" IS '小程序UNIONID';
COMMENT ON COLUMN "public"."ms_dingapp_user"."create_user" IS '创建人';
COMMENT ON COLUMN "public"."ms_dingapp_user"."create_dept" IS '创建部门';
COMMENT ON COLUMN "public"."ms_dingapp_user"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."ms_dingapp_user"."update_user" IS '修改人';
COMMENT ON COLUMN "public"."ms_dingapp_user"."update_time" IS '修改时间';
COMMENT ON COLUMN "public"."ms_dingapp_user"."status" IS '状态';
COMMENT ON COLUMN "public"."ms_dingapp_user"."is_deleted" IS '是否已删除';
COMMENT ON COLUMN "public"."ms_dingapp_user"."tenant_id" IS '租户ID';

-- ----------------------------
-- Primary Key structure for table ms_dingapp_user
-- ----------------------------
ALTER TABLE "public"."ms_dingapp_user" ADD CONSTRAINT "ms_dingapp_user_pkey" PRIMARY KEY ("id");


-- 2025-08-01 添加钉钉组织机构用户信息表
DROP TABLE IF EXISTS "public"."ms_ding_user";
CREATE TABLE "public"."ms_ding_user" (
                                         "id" int8 NOT NULL,
                                         "userid" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
                                         "name" varchar(100) COLLATE "pg_catalog"."default",
                                         "mobile" varchar(20) COLLATE "pg_catalog"."default",
                                         "title" varchar(100) COLLATE "pg_catalog"."default",
                                         "dept_id_list" text COLLATE "pg_catalog"."default",
                                         "dept_names" text COLLATE "pg_catalog"."default",
                                         "parent_dept_ids" text COLLATE "pg_catalog"."default",
                                         "create_time" timestamp(6),
                                         "update_time" timestamp(6),
                                         "status" int4 DEFAULT 1
)
;
COMMENT ON COLUMN "public"."ms_ding_user"."userid" IS '钉钉用户ID';
COMMENT ON COLUMN "public"."ms_ding_user"."name" IS '用户姓名';
COMMENT ON COLUMN "public"."ms_ding_user"."mobile" IS '手机号';
COMMENT ON COLUMN "public"."ms_ding_user"."title" IS '职位';
COMMENT ON COLUMN "public"."ms_ding_user"."dept_id_list" IS '部门ID列表（JSON格式）';
COMMENT ON COLUMN "public"."ms_ding_user"."dept_names" IS '部门名称列表（JSON格式）';
COMMENT ON COLUMN "public"."ms_ding_user"."parent_dept_ids" IS '父级部门ID列表（JSON格式）';
COMMENT ON COLUMN "public"."ms_ding_user"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."ms_ding_user"."update_time" IS '更新时间';
COMMENT ON COLUMN "public"."ms_ding_user"."status" IS '状态（1-正常，0-删除）';
COMMENT ON TABLE "public"."ms_ding_user" IS '组织机构用户信息';

-- ----------------------------
-- Indexes structure for table ms_ding_user
-- ----------------------------
CREATE INDEX "idx_create_time" ON "public"."ms_ding_user" USING btree (
    "create_time" "pg_catalog"."timestamp_ops" ASC NULLS LAST
    );
CREATE INDEX "idx_mobile" ON "public"."ms_ding_user" USING btree (
    "mobile" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );
CREATE INDEX "idx_name" ON "public"."ms_ding_user" USING btree (
    "name" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );
CREATE INDEX "idx_status" ON "public"."ms_ding_user" USING btree (
    "status" "pg_catalog"."int4_ops" ASC NULLS LAST
    );
CREATE UNIQUE INDEX "uk_userid" ON "public"."ms_ding_user" USING btree (
    "userid" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );

-- ----------------------------
-- Primary Key structure for table ms_ding_user
-- ----------------------------
ALTER TABLE "public"."ms_ding_user" ADD CONSTRAINT "ms_ding_user_pkey" PRIMARY KEY ("id");

-- 2025-08-07 增加用户的最后登录时间
ALTER TABLE "public"."blade_user"
    ADD COLUMN "last_login_time" timestamp(6);

COMMENT ON COLUMN "public"."blade_user"."last_login_time" IS '最后登录时间';