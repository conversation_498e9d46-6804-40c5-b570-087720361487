import * as dd from 'dingtalk-jsapi';
import { http } from './http';
import { showFailToast, showToast } from 'vant';

/**
 * 获取jsapi_ticket
 * @returns {Promise<string>} jsapi_ticket
 */
export const getJsapiTicket = async () => {
  try {
    const response = await http.get('/dingtalk/getJsapiTicket');
    if (response.success && response.data) {
      return response.data.ticket;
    } else {
      showFailToast('获取jsapi_ticket失败');
      return null;
    }
  } catch (error) {
    console.error('获取jsapi_ticket出错：', error);
    showFailToast('获取jsapi_ticket异常');
    return null;
  }
};

/**
 * 生成随机字符串
 * @returns {string} 随机字符串
 */
const createNonceStr = () => {
  return Math.random().toString(36).substr(2, 15);
};

/**
 * 生成时间戳
 * @returns {string} 时间戳
 */
const createTimestamp = () => {
  return '' + Math.round(new Date().getTime() / 1000);
};

/**
 * 签名算法
 * @param {Object} params - 参数对象
 * @returns {string} 签名
 */
const genSignature = async (params) => {
  const querystring = Object.keys(params)
    .sort()
    .map(key => `${key}=${encodeURIComponent(params[key])}`) // 确保URL编码
    .join('&');

    console.log('签名请求参数:', querystring);

    try {
        const response = await http.get('/api/dingapp/user/getDingTalkApiParam?'+querystring);
        console.log('签名接口响应:', response);

        if (response.signature != null) {
            return response.signature;
        } else {
            console.error('签名接口返回空值:', response);
            showFailToast('获取签名失败: 接口返回空值');
            return null;
        }
    } catch (error) {
        console.error('错误详情:', error.response?.data || error.message);
        showFailToast(`获取签名异常: ${error.message}`);
        return null;
    }
};

/**
 * 配置钉钉 JSAPI 鉴权
 * @param {string} ticket - jsapi_ticket
 * @param {string} url - 当前页面 URL（不包含#及其后面部分）
 * @returns {Promise<boolean>} 鉴权是否成功
 */
export const configDingTalkJsApi = async (url) => {
  try {

    // 准备鉴权参数
    const nonceStr = createNonceStr();
    const timestamp = createTimestamp();

    // 去除 URL 中的 # 及其后面部分
    const cleanUrl = url.split('#')[0];


    // 生成签名对象
    const signObj = {
      //jsapi_ticket: ticket,
      nonceStr: nonceStr,
      timeStamp: timestamp,
      url: cleanUrl
    };

    // 获取签名
    const signature = await genSignature(signObj);

    if (!signature) {
      throw new Error('获取签名失败');
    }

    // 配置钉钉 JSAPI
    const configParams = {
      agentId: import.meta.env.VITE_DING_AGENT_ID, // 必填，微应用ID
      corpId: import.meta.env.VITE_DING_CORP_ID,   // 必填，企业ID
      timeStamp: timestamp,                            // 必填，生成签名的时间戳
      nonceStr: nonceStr,                              // 必填，生成签名的随机串
      signature: signature,                            // 必填，签名
      jsApiList: [                                     // 必填，需要使用的jsapi列表
        'runtime.info',
        'biz.contact.choose',
        'device.geolocation.get',
        'biz.util.uploadImage',
        'biz.util.previewImage',
        'biz.util.datepicker',
        'biz.user.get',
        'biz.navigation.setTitle',
        'biz.navigation.setRight',
        'biz.cspace.saveFile',
        'biz.util.uploadAttachment',
        'biz.cspace.preview',
        'biz.cspace.previewDentryImages',
        'device.screen.rotateView',
        'biz.util.downloadFile',
        'device.download',
        'device.notification.saveImage',
        'device.base.getInterface'
      ]
    };

    console.log('钉钉配置参数:', configParams);
    dd.config(configParams);

      dd.error(function (err) {
          alert('dd error: ' + JSON.stringify(err));
      })//该方法必须带上，用来捕获鉴权出现的异常信息，否则不方便排查出现的问题

    // 注册钉钉 JSAPI 成功回调
    return new Promise((resolve) => {
      dd.ready(() => {
        console.log('钉钉 JSAPI 鉴权成功');
        resolve(true);
      });

      dd.error((err) => {
        console.error('钉钉 JSAPI 鉴权失败：', err);
        showFailToast('钉钉 JSAPI 鉴权失败');
        resolve(false);
      });
    });
  } catch (error) {
    console.error('配置钉钉 JSAPI 鉴权异常：', error);
    showFailToast('配置钉钉 JSAPI 鉴权异常');
    return false;
  }
};

/**
 * 初始化钉钉环境
 * @returns {Promise<boolean>} 初始化是否成功
 */
export const initDingTalk = async () => {
  try {
    // 获取当前页面 URL
    const url = window.location.href;
    console.log('初始化钉钉环境，当前URL:', url);


    // 配置钉钉 JSAPI 鉴权
    const result = await configDingTalkJsApi(url);

    return result;
  } catch (error) {
    console.error('初始化钉钉环境异常：', error);
    console.error('错误堆栈:', error.stack);
    showFailToast(`初始化钉钉环境异常: ${error.message}`);
    return false;
  }
};

/**
 * 设置钉钉导航栏标题
 * @param {string} title - 标题
 */
export const setDingTalkTitle = (title) => {
  dd.biz.navigation.setTitle({
    title: title,
    onSuccess: () => {
      console.log('设置标题成功');
    },
    onFail: (err) => {
      console.error('设置标题失败：', err);
    }
  });
};