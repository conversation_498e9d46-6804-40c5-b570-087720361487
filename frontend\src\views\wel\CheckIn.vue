<template>
  <!-- 签到内容 -->
  <div class="check-in-content">
    <div class="check-in-card list-item">
      <div class="check-in-icon">
        <i class="fas fa-qrcode"></i>
      </div>
      <h2>扫描二维码签到</h2>
      <p class="check-in-description">请使用手机扫描下方二维码完成会议签到</p>

      <!-- 二维码占位符 -->
      <div class="qr-code-placeholder" v-if="!isCheckedIn">
        <div class="qr-code-content">
          <div class="qr-dot"></div>
          <div class="qr-dot"></div>
          <div class="qr-dot"></div>
          <div class="qr-dot"></div>
        </div>
      </div>

      <p class="scan-tips">或将下方签到码提供给工作人员扫描</p>

      <!-- 签到码 -->
      <div class="check-in-code">
        <span class="code-digit">A</span>
        <span class="code-digit">B</span>
        <span class="code-digit">C</span>
        <span class="code-digit">1</span>
        <span class="code-digit">2</span>
        <span class="code-digit">3</span>
      </div>

      <button class="manual-check-in-btn submit-btn" @click="handleManualCheckIn">
        <i class="fas fa-edit"></i>
        手动签到
      </button>
    </div>

    <!-- 签到状态 -->
    <div class="check-in-status list-item" v-if="isCheckedIn">
      <div class="status-icon success">
        <i class="fas fa-check-circle"></i>
      </div>
      <h3>签到成功</h3>
      <p>您已于 {{ checkInTime }} 完成签到</p>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CheckIn',
  data() {
    return {
      isCheckedIn: false,
      checkInTime: ''
    }
  },
  methods: {
    goBack() {
      // 返回上一页
      this.$parent.goBack();
    },
    handleManualCheckIn() {
      // 手动签到处理
      this.isCheckedIn = true;
      this.checkInTime = new Date().toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });

      // 显示签到成功提示
      alert('签到成功！');
    }
  }
}
</script>

<style scoped>
/* 页面通用样式 - 与其他页面统一 */
.page-container {
  width: 100%;
  min-height: 85vh;
  background-size: cover;
  position: relative;
  z-index: 1;
  margin: 0 auto;
  box-sizing: border-box;
}

.check-in-container {
  width: 100%;
  min-height: 100vh;
  padding: 20px;
  box-sizing: border-box;
}

.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 0;
  margin-bottom: 20px;
}

.page-header h1 {
  font-size: 20px;
  font-weight: 600;
  color: #ffffff;
  flex: 1;
  text-align: center;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.back-btn {
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(7, 211, 240, 0.3);
  color: #07D3F0;
  font-size: 20px;
  cursor: pointer;
  padding: 5px 10px;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.back-btn:hover {
  background: rgba(7, 211, 240, 0.2);
  transform: translateY(-2px);
}

.header-placeholder {
  width: 40px;
}

.check-in-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 30px;
  margin-top: 50px;
  padding: 0 15px;
}

/* 统一卡片样式 */
.list-item {
  background: rgba(255, 255, 255, 0.06);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 30px 20px;
  margin-bottom: 15px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-left: 4px solid rgba(7, 211, 240, 0.7);
  transition: all 0.3s ease;
  animation: slideInUp 0.6s ease forwards;
  position: relative;
  overflow: hidden;
  width: 100%;
  max-width: 400px;
  text-align: center;
}

/* 卡片悬停效果 */
.list-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: -150%;
  width: 300%;
  height: 100%;
  background: linear-gradient(
      120deg,
      rgba(255, 255, 255, 0) 0%,
      rgba(255, 255, 255, 0.2) 50%,
      rgba(255, 255, 255, 0) 100%
  );
  transform: skewX(-20deg);
  z-index: 1;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.list-item:hover::before {
  opacity: 1;
  animation: shimmer 1.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.list-item:hover {
  transform: translateX(3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  background: rgba(255, 255, 255, 0.09);
}

.check-in-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, rgba(7, 211, 240, 0.3), rgba(7, 211, 240, 0.15));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  border: 1px solid rgba(7, 211, 240, 0.3);
}

.check-in-icon i {
  font-size: 40px;
  color: #07D3F0;
  text-shadow: 0 0 15px rgba(7, 211, 240, 0.6);
}

.check-in-card h2 {
  font-size: 22px;
  color: #ffffff;
  margin-bottom: 10px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.check-in-description {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  margin-bottom: 30px;
  line-height: 1.5;
}

.qr-code-placeholder {
  width: 200px;
  height: 200px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
  margin: 0 auto 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px dashed rgba(7, 211, 240, 0.3);
}

.qr-code-content {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 60px;
}

.qr-dot {
  width: 40px;
  height: 40px;
  background: #07D3F0;
  border-radius: 5px;
  box-shadow: 0 0 10px rgba(7, 211, 240, 0.6);
}

.scan-tips {
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
  margin: 20px 0;
}

.check-in-code {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin: 20px 0;
}

.code-digit {
  width: 40px;
  height: 50px;
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(7, 211, 240, 0.3);
  border-radius: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  font-weight: bold;
  color: #07D3F0;
}

/* 统一按钮样式 */
.submit-btn {
  background: linear-gradient(135deg, rgba(7, 211, 240, 0.3), rgba(7, 211, 240, 0.15));
  color: white;
  border: 1px solid rgba(7, 211, 240, 0.3);
  border-radius: 30px;
  padding: 12px 30px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin: 20px auto 0;
  position: relative;
  overflow: hidden;
}

.submit-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -150%;
  width: 300%;
  height: 100%;
  background: linear-gradient(
      120deg,
      rgba(255, 255, 255, 0) 0%,
      rgba(255, 255, 255, 0.2) 50%,
      rgba(255, 255, 255, 0) 100%
  );
  transform: skewX(-20deg);
  z-index: 1;
  pointer-events: none;
  opacity: 0;
}

.submit-btn:hover::before {
  opacity: 1;
  animation: shimmer 1.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.submit-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(7, 211, 240, 0.2);
}

.check-in-status h3 {
  font-size: 22px;
  color: #ffffff;
  margin-bottom: 10px;
}

.check-in-status p {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
}

.status-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  font-size: 40px;
}

.status-icon.success {
  background: rgba(46, 204, 113, 0.2);
  color: #2ecc71;
  border: 1px solid rgba(46, 204, 113, 0.3);
}

/* 动画效果统一 */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
  }
  100% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
  }
}
</style>
